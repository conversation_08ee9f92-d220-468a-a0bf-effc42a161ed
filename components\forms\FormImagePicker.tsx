import React, { useEffect, useState, useRef } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  StyleProp,
  ViewStyle,
  Alert,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { useController, Control, FieldValues, Path } from "react-hook-form";
import * as ImagePicker from "expo-image-picker";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import { useCreateSignedUploadUrlMutation } from "@/generated/graphql";

interface FormImagePickerProps<T extends FieldValues> {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  containerStyle?: StyleProp<ViewStyle>;
  rules?: object;
}

export type ImageState = {
  id: string;
  uri: string; // Local URI or S3 URL
  isUploading: boolean;
  progress: number;
  isS3Url: boolean; // Track if this is already an S3 URL
  fileName?: string; // Original filename from picker
  mimeType?: string; // MIME type from picker
};

export function FormImagePicker<T extends FieldValues>({
  name,
  control,
  label,
  placeholder = "Take a photo or upload",
  containerStyle,
  rules,
}: FormImagePickerProps<T>) {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
  });

  const [imageStates, setImageStates] = useState<ImageState[]>([]);
  const { mutateAsync: getSignedUrl } = useCreateSignedUploadUrlMutation();
  const isInitializedRef = useRef(false);

  // Initialize image states from form value (only on mount)
  useEffect(() => {
    const currentUrls: string[] = field.value || [];

    // Only initialize once on mount if there are existing URLs
    if (!isInitializedRef.current) {
      isInitializedRef.current = true;
      if (currentUrls.length > 0) {
        const newImageStates: ImageState[] = currentUrls.map((url, index) => ({
          id: `${index}-${url}`,
          uri: url,
          isUploading: false,
          progress: 100,
          isS3Url: url.startsWith("http"), // Assume URLs starting with http are S3 URLs
        }));
        setImageStates(newImageStates);
      }
    }
  }, [field.value]);

  const uploadImageToS3 = async (imageState: ImageState): Promise<string> => {
    try {
      // Use file info from image state or fallback to defaults
      const fileName = imageState.fileName || `image-${Date.now()}.jpg`;
      const mimeType = imageState.mimeType || "image/jpeg";

      // Get signed URL
      const data = await getSignedUrl({
        input: {
          key: `uploads/${Date.now()}-${fileName}`,
          contentType: mimeType,
          expiresIn: 300,
        },
      });

      if (
        !data?.createSignedUploadUrl?.url ||
        !data?.createSignedUploadUrl?.fields
      ) {
        throw new Error("Failed to get upload URL");
      }

      // Create FormData for React Native
      const formData = new FormData();
      const fields = data.createSignedUploadUrl.fields;

      // Add fields in correct order for S3
      formData.append("key", fields.key);
      formData.append("bucket", fields.bucket);
      formData.append("acl", fields.acl);
      formData.append("X-Amz-Algorithm", fields.algorithm);
      formData.append("X-Amz-Credential", fields.credential);
      formData.append("X-Amz-Date", fields.date);
      formData.append("Policy", fields.Policy);
      formData.append("X-Amz-Signature", fields.signature);
      formData.append("Content-Type", mimeType);

      // For React Native, append the file as an object with uri, type, and name
      const fileObject = {
        uri: imageState.uri,
        type: mimeType,
        name: fileName,
      };
      formData.append("file", fileObject as any);

      // Upload using XMLHttpRequest with progress tracking
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open("POST", data.createSignedUploadUrl.url);

        xhr.upload.addEventListener("progress", (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setImageStates((current) =>
              current.map((img) =>
                img.id === imageState.id ? { ...img, progress } : img
              )
            );
          }
        });

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            // Construct the final S3 URL
            const s3Url = `https://${fields.bucket}.s3.amazonaws.com/${fields.key}`;
            resolve(s3Url);
          } else {
            reject(new Error("Upload failed"));
          }
        };

        xhr.onerror = () => reject(new Error("Upload failed"));
        xhr.send(formData);
      });
    } catch (error) {
      console.error("Upload failed:", error);
      throw error;
    }
  };

  const handlePickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission Required", "We need access to your gallery.");
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsMultipleSelection: true,
      selectionLimit: 0,
      allowsEditing: false,
      quality: 0.8,
    });

    if (!result.canceled) {
      await processNewImages(
        result.assets.map((asset) => ({
          uri: asset.uri,
          fileName: asset.fileName || `image-${Date.now()}.jpg`,
          mimeType: asset.mimeType || "image/jpeg",
        }))
      );
    }
  };

  const handleTakePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission Required", "We need camera access.");
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ["images"],
      allowsEditing: false,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      const asset = result.assets[0];
      await processNewImages([
        {
          uri: asset.uri,
          fileName: asset.fileName || `image-${Date.now()}.jpg`,
          mimeType: asset.mimeType || "image/jpeg",
        },
      ]);
    }
  };

  const processNewImages = async (
    imageInfos: Array<{ uri: string; fileName: string; mimeType: string }>
  ) => {
    // Create new image states for the selected images
    const newImageStates: ImageState[] = imageInfos.map((imageInfo) => ({
      id: `${Date.now()}-${Math.random()}`,
      uri: imageInfo.uri,
      isUploading: true,
      progress: 0,
      isS3Url: false,
      fileName: imageInfo.fileName,
      mimeType: imageInfo.mimeType,
    }));

    // Add to current image states
    setImageStates((current) => [...current, ...newImageStates]);

    // Upload each image
    for (const imageState of newImageStates) {
      try {
        const s3Url = await uploadImageToS3(imageState);

        // Update the image state with the S3 URL
        setImageStates((current) =>
          current.map((img) =>
            img.id === imageState.id
              ? {
                  ...img,
                  uri: s3Url,
                  isUploading: false,
                  isS3Url: true,
                  progress: 100,
                }
              : img
          )
        );

        // Update form field with S3 URLs
        const currentUrls = field.value || [];
        field.onChange([...currentUrls, s3Url]);
      } catch (error) {
        console.error("Failed to upload image:", error);
        // Remove failed upload from image states
        setImageStates((current) =>
          current.filter((img) => img.id !== imageState.id)
        );
        Alert.alert(
          "Upload Failed",
          "Failed to upload image. Please try again."
        );
      }
    }
  };

  const handleRemoveImage = (imageId: string) => {
    const imageToRemove = imageStates.find((img) => img.id === imageId);
    if (!imageToRemove) return;

    // Remove from image states
    setImageStates((current) => current.filter((img) => img.id !== imageId));

    // Remove from form field
    const currentUrls = field.value || [];
    const updatedUrls = currentUrls.filter(
      (url: string) => url !== imageToRemove.uri
    );
    field.onChange(updatedUrls);
  };

  const showImageSourceOptions = () => {
    Alert.alert("Add Image", "Choose an option", [
      { text: "Take Photo", onPress: handleTakePhoto },
      { text: "Choose from Gallery", onPress: handlePickImage },
      { text: "Cancel", style: "cancel" },
    ]);
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && <Text style={styles.label}>{label}</Text>}
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {imageStates.map((imageState) => (
          <View key={imageState.id} style={styles.imageWrapper}>
            <Image source={{ uri: imageState.uri }} style={styles.image} />

            {/* Upload progress indicator */}
            {imageState.isUploading && (
              <View style={styles.progressOverlay}>
                <ActivityIndicator size="small" color={Colors.primary} />
                <Text style={styles.progressText}></Text>
              </View>
            )}

            {/* Remove button */}
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => handleRemoveImage(imageState.id)}
              disabled={imageState.isUploading}
            >
              <Ionicons name="close-circle" size={20} color="red" />
            </TouchableOpacity>
          </View>
        ))}

        {/* Add image button */}
        <TouchableOpacity
          style={styles.addImageButton}
          onPress={showImageSourceOptions}
        >
          <Ionicons
            name="add-circle-outline"
            size={40}
            color={Colors.primary}
          />
          <Text style={styles.uploadText}>{placeholder}</Text>
        </TouchableOpacity>
      </ScrollView>
      {error && <Text style={styles.errorText}>{error.message}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.text,
    marginBottom: 8,
  },
  imageWrapper: {
    position: "relative",
    marginRight: 10,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  progressOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  progressText: {
    color: "white",
    fontSize: 12,
    marginTop: 4,
    fontWeight: "500",
  },
  removeButton: {
    position: "absolute",
    top: -5,
    right: -5,
    backgroundColor: "white",
    borderRadius: 10,
  },
  addImageButton: {
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 12,
  },
  uploadText: {
    marginTop: 4,
    fontSize: 12,
    color: Colors.primary,
    textAlign: "center",
  },
  errorText: {
    fontSize: 12,
    color: Colors.error || "red",
    marginTop: 4,
  },
});
