import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Alert,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
  Text,
  TouchableOpacity,
} from "react-native";
import { useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import {
  FormInput,
  FormSubmitButton,
  FormSelect,
  FormEvidenceUpload,
} from "@/components/forms";
import {
  incidentReportSchema,
  IncidentReportFormData,
  defaultIncidentReportValues,
  getPriorityLevelOptions,
  EvidenceType,
  getEvidenceTypeOptions,
} from "@/schemas/incident-reporting";
import { errorToast, successToast } from "@/lib/utils";
import { useCreateIncidentMutation } from "@/generated/graphql";

export default function IncidentReportingScreen() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    reset,
    formState: { isValid },
  } = useForm<IncidentReportFormData>({
    defaultValues: defaultIncidentReportValues,
    resolver: zodResolver(incidentReportSchema),
    mode: "onChange",
  });

  // Field array for evidence management
  const { fields, append, remove } = useFieldArray({
    control,
    name: "evidence",
  });

  // GraphQL mutation
  const createIncidentMutation = useCreateIncidentMutation({
    onSuccess: () => {
      successToast("Incident reported successfully!");
      reset();
      setIsSubmitting(false);
    },
    onError: (error) => {
      errorToast(error.message || "Failed to submit incident report");
      setIsSubmitting(false);
    },
  });

  // Handle form submission
  const onSubmit = async (data: IncidentReportFormData) => {
    try {
      setIsSubmitting(true);

      // Transform data to match GraphQL mutation input
      const mutationInput = {
        description: data.description,
        priorityLevel: data.priorityLevel,
        evidence: data.evidence,
      };

      await createIncidentMutation.mutateAsync({
        input: mutationInput,
      });
    } catch (err) {
      console.error("Form submission error:", err);
      setIsSubmitting(false);
    }
  };

  // Add new evidence item
  const addEvidence = () => {
    append({
      type: EvidenceType.Image,
      url: "",
    });
  };

  return (
    <View style={styles.flex}>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        keyboardVerticalOffset={100}
        style={styles.container}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Description Input */}
          <FormInput
            name="description"
            control={control}
            label="Description"
            placeholder="Describe the incident in detail..."
            multiline
            numberOfLines={4}
          />

          {/* Priority Level Dropdown */}
          <FormSelect
            name="priorityLevel"
            control={control}
            label="Priority Level"
            placeholder="Select priority level"
            options={getPriorityLevelOptions()}
          />

          {/* Evidence Section */}
          <View style={styles.evidenceSection}>
            <View style={styles.evidenceHeader}>
              <Text style={styles.evidenceLabel}>Evidence</Text>
              <TouchableOpacity
                style={styles.addEvidenceButton}
                onPress={addEvidence}
              >
                <Ionicons name="add" size={20} color={Colors.primary} />
                <Text style={styles.addEvidenceText}>Add Evidence</Text>
              </TouchableOpacity>
            </View>

            {fields.map((field, index) => (
              <View key={field.id} style={styles.evidenceItem}>
                <View style={styles.evidenceItemHeader}>
                  <Text style={styles.evidenceItemTitle}>
                    Evidence {index + 1}
                  </Text>
                  <TouchableOpacity
                    style={styles.removeEvidenceButton}
                    onPress={() => remove(index)}
                  >
                    <Ionicons name="trash" size={16} color={Colors.error} />
                  </TouchableOpacity>
                </View>

                <FormSelect
                  name={`evidence.${index}.type`}
                  control={control}
                  label="Evidence Type"
                  placeholder="Select type"
                  options={getEvidenceTypeOptions()}
                />

                <FormEvidenceUpload
                  name={`evidence.${index}.url`}
                  control={control}
                  label="Upload Evidence"
                  placeholder="Upload evidence file"
                  evidenceType={EvidenceType.Image}
                />
              </View>
            ))}

            {fields.length === 0 && (
              <Text style={styles.noEvidenceText}>
                No evidence added yet. Tap "Add Evidence" to include supporting
                materials.
              </Text>
            )}
          </View>

          {/* Submit Button */}
          <FormSubmitButton
            submitLabel={isSubmitting ? "Submitting..." : "Submit Report"}
            onSubmit={handleSubmit(onSubmit)}
            isValid={isValid && !isSubmitting}
            style={styles.submitButton}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 50,
    paddingTop: 16,
    paddingHorizontal: 16,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  submitButton: {
    marginTop: 20,
  },
  evidenceSection: {
    marginTop: 20,
  },
  evidenceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  evidenceLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
  },
  addEvidenceButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 4,
  },
  addEvidenceText: {
    fontSize: 14,
    fontWeight: "500",
    color: Colors.primary,
  },
  evidenceItem: {
    backgroundColor: Colors.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  evidenceItemHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  evidenceItemTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.text,
  },
  removeEvidenceButton: {
    padding: 4,
  },
  noEvidenceText: {
    fontSize: 14,
    color: Colors.textLight,
    textAlign: "center",
    fontStyle: "italic",
    paddingVertical: 20,
  },
  flex: { flex: 1 },
});
