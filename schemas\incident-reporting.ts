import { z } from "zod";
import { PriorityLevel, EvidenceType } from "@/generated/graphql";

// Re-export the GraphQL types for convenience
export { PriorityLevel, EvidenceType };

// Define incident types (for reference, not used in the form directly)
export enum IncidentType {
  SAFETY = "safety",
  SECURITY = "security",
  MAINTENANCE = "maintenance",
  ENVIRONMENTAL = "environmental",
  OTHER = "other",
}

// Evidence schema for file uploads
export const evidenceSchema = z.object({
  type: z.nativeEnum(EvidenceType),
  url: z.string().url("Invalid URL format"),
});

// Schema for incident reporting form
export const incidentReportSchema = z.object({
  description: z
    .string()
    .min(10, { message: "Description must be at least 10 characters" })
    .max(1000, { message: "Description must be less than 1000 characters" }),
  priorityLevel: z.nativeEnum(PriorityLevel, {
    errorMap: () => ({ message: "Please select a priority level" }),
  }),
  evidence: z.array(evidenceSchema).default([]),
});

// Type for incident report form
export type IncidentReportFormData = z.infer<typeof incidentReportSchema>;

// Type for evidence
export type EvidenceData = z.infer<typeof evidenceSchema>;

// Default values for incident report form
export const defaultIncidentReportValues: IncidentReportFormData = {
  description: "",
  priorityLevel: PriorityLevel.Medium,
  evidence: [],
};

// Helper function to get priority level options for dropdown
export const getPriorityLevelOptions = () => {
  return Object.entries(PriorityLevel).map(([key, value]) => ({
    label: key.toLowerCase().replace(/\b\w/g, (l) => l.toUpperCase()),
    value,
  }));
};

// Helper function to get evidence type options
export const getEvidenceTypeOptions = () => {
  return Object.entries(EvidenceType).map(([key, value]) => ({
    label: key.toLowerCase().replace(/\b\w/g, (l) => l.toUpperCase()),
    value,
  }));
};
