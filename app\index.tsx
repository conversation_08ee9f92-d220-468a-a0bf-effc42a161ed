import { MenuGrid } from "@/components/MenuGrid";
import { ProfileHeader } from "@/components/ProfileHeader";
import { Link, Redirect, useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import React, { useEffect } from "react";
import {
  FlatList,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withTiming,
} from "react-native-reanimated";

import { AnnouncementItem } from "@/components/AnnouncementItem";
import { Colors } from "@/constants/Colors";
import { AnnouncementsQuery, useAnnouncementsQuery } from "@/generated/graphql";
import { useSession } from "@/providers/auth-provider";
import { useMeQuery } from "@/generated/graphql";

// Sample data for menu items
const menuItems = [
  { icon: "people-outline", label: "Attendance", href: "/attendance" },
  { icon: "calendar-outline", label: "Leave", href: "/leave" },
  { icon: "time-outline", label: "Shifts", href: "/shifts" },
  { icon: "card-outline", label: "Payments", href: "/payments" },
  { icon: "document-text-outline", label: "Documents", href: "/documents" },
  { icon: "calendar-clear-outline", label: "Calendar", href: "/calendar" },
  { icon: "mail-outline", label: "Inbox", href: "/inbox" },
  { icon: "person-outline", label: "Account", href: "/account" },
];

/**
 * Home screen component with user profile, menu grid, and announcements
 */
export default function HomeScreen() {
  // Get session from auth provider
  const { data: userData } = useMeQuery();
  const { session, isLoading: userLoading } = useSession();

  const router = useRouter();

  const { data: announcementsData, isLoading } = useAnnouncementsQuery();

  const handleAnnouncementPress = (
    announcement: AnnouncementsQuery["anouncements"][number]
  ) => {
    router.push(`/inbox/${announcement.id}` as any);
  };

  // Animation values - only use translation animations, no opacity/fade
  const profileTranslateY = useSharedValue(20);
  const announcementsTranslateY = useSharedValue(30);
  const headerOpacity = useSharedValue(0);
  const headerTranslateY = useSharedValue(-20);

  const profileAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: profileTranslateY.value }],
  }));

  const menuAnimatedStyle = useAnimatedStyle(() => ({
    // No animations for menu container - individual items animate themselves
  }));

  const announcementsAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: announcementsTranslateY.value }],
  }));

  // Start animations when component mounts
  useEffect(() => {
    // Only slide animations, no fading
    profileTranslateY.value = withDelay(
      200,
      withTiming(0, { duration: 800, easing: Easing.out(Easing.ease) })
    );

    announcementsTranslateY.value = withDelay(
      800,
      withTiming(0, { duration: 800, easing: Easing.out(Easing.ease) })
    );
    headerOpacity.value = withTiming(1, {
      duration: 400,
      easing: Easing.out(Easing.ease),
    });
    headerTranslateY.value = withTiming(0, {
      duration: 400,
      easing: Easing.out(Easing.ease),
    });
  }, []);
  // Header animated style
  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [{ translateY: headerTranslateY.value }],
  }));

  // Redirect to login if not authenticated
  if (!isLoading && (!session || session === null)) {
    return <Redirect href="/login" />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />

      <Animated.View style={[styles.headerBackground, headerAnimatedStyle]} />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Profile Header - Animated */}
        <Animated.View style={profileAnimatedStyle}>
          <ProfileHeader
            name={session?.fullname || ""}
            role="default"
            avatarUrl={userData?.me.profilePicture!}
          />
        </Animated.View>

        {/* Menu Grid - Animated */}
        <Animated.View style={menuAnimatedStyle}>
          <MenuGrid items={menuItems} />
        </Animated.View>

        {/* Announcements - Animated */}
        <Animated.View style={announcementsAnimatedStyle}>
          <View style={styles.announcementContainer}>
            <Animated.View style={[styles.header, headerAnimatedStyle]}>
              <Text style={styles.title}>Announcements</Text>
              <Link href="/inbox" asChild>
                <TouchableOpacity>
                  <Text style={styles.viewAll}>View All</Text>
                </TouchableOpacity>
              </Link>
            </Animated.View>

            <FlatList
              data={announcementsData?.anouncements || []}
              refreshing={isLoading}
              keyExtractor={(item) => item.id}
              ItemSeparatorComponent={() => (
                <View style={styles.separatorContainer}>
                  <View style={styles.separator} />
                </View>
              )}
              renderItem={({ item }) => (
                <AnnouncementItem
                  announcement={item as any}
                  onPress={handleAnnouncementPress as any}
                />
              )}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </Animated.View>

        {/* Add padding at the bottom for scrolling */}
        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  announcementContainer: {
    marginTop: 24,
    backgroundColor: Colors.white,
    borderRadius: 8,
    overflow: "hidden",
    paddingHorizontal: 16,
    height: "100%",
  },
  headerBackground: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    height: 200,
    backgroundColor: Colors.primary,
    // On Android, zIndex: -1 can cause issues with rendering
    // Using a higher zIndex and ensuring it's behind other elements
    zIndex: 0,
  },
  scrollView: {
    flex: 1,
    zIndex: 1, // Ensure the scroll view is above the header background
  },
  scrollContent: {
    paddingBottom: 20,
  },
  bottomPadding: {
    height: 40,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.text,
  },
  viewAll: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: "500",
  },
  separatorContainer: {
    backgroundColor: Colors.white,
  },
  separator: {
    height: 1,
    backgroundColor: Colors.border,
  },
});
