/**
 * App color palette
 */
export const Colors = {
  primary: "#27AE60",
  primaryDark: "#219653",
  primaryLight: "#6FCF97",
  secondary: "#4A6FEB",
  secondaryLight: "#6F8CF9",
  background: "#F9FAFB",
  white: "#FFFFFF",
  text: "#333333",
  textSecondary: "#666666",
  textLight: "#999999",
  border: "#E0E0E0",
  diabledBg: "#E0E0E0",
  lightGray: "#F2F2F2",
  error: "#EB5757",
  notification: "#F2994A",
  warning: "#F2C94C",
  success: "#27AE60",
  successLight: "#D4F4DD",
  cardBackground: "#FFFFFF",
  headerBackground: "#27AE60",
};

/**
 * Get color with opacity
 * @param hex Hex color code
 * @param alpha Opacity value (0-1)
 * @returns RGBA color string
 */
export function hexToRGBA(hex: string, alpha: number): string {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}
