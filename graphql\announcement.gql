query Announcements {
  anouncements {
    id
    title
    description
    date
    userRoles
    document
    createdBy {
      id
      fullname
      role
      profilePicture
    }
  }
}

query AnnouncementDetail($id: String!) {
  anouncement(id: $id) {
    title
    description
    document
    createdAt
    document
    _id
    users {
      id
      createdAt
      updatedAt
      fullname
      phone
      userStatus
      role
    }
    createdBy {
      id
      fullname
      role
      profilePicture
    }
  }
}
