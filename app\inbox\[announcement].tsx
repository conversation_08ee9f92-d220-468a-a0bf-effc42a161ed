import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Linking,
  Image,
} from "react-native";
import React, { useEffect, useState } from "react";
import { useLocalSearchParams } from "expo-router";
import { useAnnouncementDetailQuery } from "@/generated/graphql";
import LoadingScreen from "@/components/LoadingView";
import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import { format } from "date-fns";
import { openBrowserAsync } from "expo-web-browser";
import * as Haptics from "expo-haptics";
import { extractInitials } from "@/lib/utils";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withDelay,
} from "react-native-reanimated";

export default function InboxDetailScreen() {
  const { announcement } = useLocalSearchParams();
  const { data, isLoading } = useAnnouncementDetailQuery({
    id: announcement as string,
  });

  const [isDownloading, setIsDownloading] = useState(false);

  // Animation values for loading
  const loadingOpacity = useSharedValue(1);

  // Animate loading dots
  useEffect(() => {
    if (isDownloading) {
      loadingOpacity.value = withTiming(0.3, { duration: 500 });
      const interval = setInterval(() => {
        loadingOpacity.value = withTiming(
          loadingOpacity.value === 1 ? 0.3 : 1,
          { duration: 500 }
        );
      }, 500);
      return () => clearInterval(interval);
    } else {
      loadingOpacity.value = 1;
    }
  }, [isDownloading]);

  const loadingAnimatedStyle = useAnimatedStyle(() => ({
    opacity: loadingOpacity.value,
  }));

  // Handle document download/view
  const handleDocumentPress = async () => {
    if (!data?.anouncement?.document) return;

    // Add haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    const documentUrl = data.anouncement.document;

    Alert.alert(
      "View Attachment",
      "Choose how you want to view this document:",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Open in Browser",
          onPress: () => openInBrowser(documentUrl),
        },
        {
          text: "Download",
          onPress: () => downloadDocument(documentUrl),
        },
      ]
    );
  };

  const openInBrowser = async (url: string) => {
    try {
      await openBrowserAsync(url);
    } catch (error) {
      Alert.alert("Error", "Failed to open document in browser");
    }
  };

  const downloadDocument = async (url: string) => {
    try {
      setIsDownloading(true);

      // For now, we'll open the URL in the browser with download intent
      // In a production app, you would implement proper file download
      await Linking.openURL(url);

      Alert.alert(
        "Download Started",
        "The document download has been initiated. Check your device's download folder."
      );
    } catch (error) {
      Alert.alert("Error", "Failed to download document");
    } finally {
      setIsDownloading(false);
    }
  };

  // Animation values
  const headerTranslateY = useSharedValue(-20);
  const contentTranslateY = useSharedValue(20);

  const headerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: headerTranslateY.value }],
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: contentTranslateY.value }],
  }));

  // Start animations when data loads
  useEffect(() => {
    if (!isLoading && data?.anouncement) {
      // Header animation
      headerTranslateY.value = withDelay(
        100,
        withSpring(0, { damping: 12, stiffness: 100 })
      );

      // Content animation
      contentTranslateY.value = withDelay(
        200,
        withSpring(0, { damping: 12, stiffness: 100 })
      );
    }
  }, [isLoading, data]);

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!data?.anouncement) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons
            name="alert-circle-outline"
            size={64}
            color={Colors.error}
          />
          <Text style={styles.errorText}>Announcement not found</Text>
        </View>
      </View>
    );
  }

  const announcementData = data.anouncement;

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.content}>
        {/* Header Section */}
        <Animated.View style={[styles.header, headerAnimatedStyle]}>
          <Text style={styles.title}>{announcementData.title}</Text>
        </Animated.View>

        {/* Content Section */}
        <Animated.View style={[styles.contentSection, contentAnimatedStyle]}>
          {/* Posted By Info */}
          {announcementData.createdBy && (
            <View style={styles.postedByContainer}>
              {/* Profile Picture or Initials */}
              <Text style={styles.postedByText}>Posted by</Text>
              <View style={styles.postedByTextContainer}>
                <View style={styles.creatorAvatarContainer}>
                  {announcementData.createdBy.profilePicture ? (
                    <Image
                      source={{
                        uri: announcementData.createdBy.profilePicture,
                      }}
                      style={styles.creatorAvatar}
                      resizeMode="cover"
                    />
                  ) : (
                    <View style={styles.creatorInitialsContainer}>
                      <Text style={styles.creatorInitials}>
                        {extractInitials(announcementData.createdBy.fullname)}
                      </Text>
                    </View>
                  )}
                </View>
                <Text style={styles.creatorName}>
                  {announcementData.createdBy.fullname}
                </Text>
              </View>
            </View>
          )}

          {/* Date Info */}
          <View style={styles.dateContainer}>
            <Ionicons
              name="calendar-outline"
              size={16}
              color={Colors.textLight}
            />
            <Text style={styles.dateText}>
              Posted on{" "}
              {announcementData.createdAt
                ? format(
                    new Date(announcementData.createdAt),
                    "MMMM dd, yyyy 'at' h:mm a"
                  )
                : "Unknown date"}
            </Text>
          </View>

          {/* Description */}
          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionLabel}>Description</Text>
            <Text style={styles.description}>
              {announcementData.description}
            </Text>
          </View>

          {/* Document Section */}
          {announcementData.document && (
            <TouchableOpacity
              style={[
                styles.documentContainer,
                isDownloading && styles.documentContainerDisabled,
              ]}
              onPress={handleDocumentPress}
              disabled={isDownloading}
              activeOpacity={0.7}
            >
              <View style={styles.documentIcon}>
                <Ionicons
                  name="document-text"
                  size={20}
                  color={isDownloading ? Colors.textLight : Colors.primary}
                />
              </View>
              <View style={styles.documentInfo}>
                <Text style={styles.documentTitle}>Attachment</Text>
                <Text style={styles.documentSubtitle}>
                  {isDownloading ? "Processing..." : "Tap to view or download"}
                </Text>
              </View>
              {isDownloading ? (
                <Animated.View
                  style={[styles.loadingContainer, loadingAnimatedStyle]}
                >
                  <Text style={styles.loadingText}>•••</Text>
                </Animated.View>
              ) : (
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={Colors.textLight}
                />
              )}
            </TouchableOpacity>
          )}

          {/* Targeted Users Section */}
          {announcementData.users && announcementData.users.length > 0 && (
            <View style={styles.usersContainer}>
              <Text style={styles.usersLabel}>Users Targeted</Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.usersScrollView}
                contentContainerStyle={styles.usersScrollContent}
              >
                {announcementData.users.map((user) => (
                  <View key={user.id} style={styles.userItem}>
                    <View style={styles.userAvatar}>
                      <Text style={styles.userInitials}>
                        {user.fullname
                          .split(" ")
                          .map((n) => n[0])
                          .join("")
                          .toUpperCase()}
                      </Text>
                    </View>
                    <Text style={styles.userName} numberOfLines={1}>
                      {user.fullname}
                    </Text>
                  </View>
                ))}
              </ScrollView>
            </View>
          )}
        </Animated.View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: "flex-start",
    marginBottom: 24,
    paddingTop: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: Colors.text,
    textAlign: "left",
    lineHeight: 32,
  },
  contentSection: {
    gap: 24,
  },
  postedByContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    marginBottom: 4,
  },
  creatorAvatarContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    overflow: "hidden",
  },
  creatorAvatar: {
    width: "100%",
    height: "100%",
  },
  creatorInitialsContainer: {
    width: "100%",
    height: "100%",
    backgroundColor: Colors.primaryLight,
    justifyContent: "center",
    alignItems: "center",
  },
  creatorInitials: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.white,
  },
  postedByTextContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  postedByText: {
    fontSize: 12,
    color: Colors.textLight,
    fontWeight: "500",
  },
  creatorName: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: "600",
    textTransform: "capitalize",
  },
  dateContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginBottom: 8,
  },
  dateText: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontWeight: "500",
  },
  descriptionContainer: {
    gap: 12,
    alignItems: "flex-start",
  },
  descriptionLabel: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.text,
    textAlign: "left",
  },
  description: {
    fontSize: 16,
    lineHeight: 26,
    color: Colors.textSecondary,
    textAlign: "left",
  },
  documentContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.background,
    padding: 18,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border,
    gap: 12,
  },
  documentIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.primaryLight,
    justifyContent: "center",
    alignItems: "center",
  },
  documentInfo: {
    flex: 1,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
  },
  documentSubtitle: {
    fontSize: 14,
    color: Colors.textLight,
    marginTop: 2,
  },
  documentContainerDisabled: {
    opacity: 0.6,
    backgroundColor: Colors.lightGray,
  },
  loadingContainer: {
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 12,
    color: Colors.textLight,
    fontWeight: "bold",
  },
  usersContainer: {
    gap: 12,
  },
  usersLabel: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.text,
    textAlign: "left",
    marginBottom: 8,
  },
  usersScrollView: {
    flexGrow: 0,
  },
  usersScrollContent: {
    paddingRight: 20,
  },
  userItem: {
    alignItems: "center",
    marginRight: 16,
    width: 80,
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.primaryLight,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  userInitials: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.white,
  },
  userName: {
    fontSize: 12,
    fontWeight: "500",
    color: Colors.text,
    textAlign: "center",
  },
  userInfo: {
    flex: 1,
  },
  userRole: {
    fontSize: 12,
    color: Colors.textLight,
    textTransform: "capitalize",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  errorText: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.textSecondary,
    textAlign: "center",
  },
});
