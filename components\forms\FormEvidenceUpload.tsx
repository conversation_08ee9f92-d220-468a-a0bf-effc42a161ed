import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from "react-native";
import { useController, Control, FieldValues, Path } from "react-hook-form";
import * as ImagePicker from "expo-image-picker";
// import * as DocumentPicker from "expo-document-picker"; // TODO: Install expo-document-picker
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import { useCreateSignedUploadUrlMutation } from "@/generated/graphql";
import { EvidenceType } from "@/schemas/incident-reporting";

interface FormEvidenceUploadProps<T extends FieldValues> {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  evidenceType: EvidenceType;
}

export function FormEvidenceUpload<T extends FieldValues>({
  name,
  control,
  label,
  placeholder = "Upload evidence",
  evidenceType,
}: FormEvidenceUploadProps<T>) {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const getSignedUrl = useCreateSignedUploadUrlMutation();

  const uploadFileToS3 = async (
    fileUri: string,
    fileName: string,
    mimeType: string
  ): Promise<string> => {
    try {
      // Get signed URL
      const data = await getSignedUrl.mutateAsync({
        input: {
          key: `evidence/${Date.now()}-${fileName}`,
          contentType: mimeType,
          expiresIn: 300,
        },
      });

      if (
        !data?.createSignedUploadUrl?.url ||
        !data?.createSignedUploadUrl?.fields
      ) {
        throw new Error("Failed to get upload URL");
      }

      // Create FormData for React Native
      const formData = new FormData();
      const fields = data.createSignedUploadUrl.fields;

      // Add fields in correct order for S3
      formData.append("key", fields.key);
      formData.append("bucket", fields.bucket);
      formData.append("acl", fields.acl);
      formData.append("X-Amz-Algorithm", fields.algorithm);
      formData.append("X-Amz-Credential", fields.credential);
      formData.append("X-Amz-Date", fields.date);
      formData.append("Policy", fields.Policy);
      formData.append("X-Amz-Signature", fields.signature);
      formData.append("Content-Type", mimeType);

      // For React Native, append the file as an object with uri, type, and name
      const fileObject = {
        uri: fileUri,
        type: mimeType,
        name: fileName,
      };
      formData.append("file", fileObject as any);

      // Upload using XMLHttpRequest with progress tracking
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open("POST", data.createSignedUploadUrl.url);

        xhr.upload.addEventListener("progress", (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setUploadProgress(progress);
          }
        });

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            // Construct the final S3 URL
            const s3Url = `https://${fields.bucket}.s3.amazonaws.com/${fields.key}`;
            resolve(s3Url);
          } else {
            reject(new Error("Upload failed"));
          }
        };

        xhr.onerror = () => reject(new Error("Upload failed"));
        xhr.send(formData);
      });
    } catch (error) {
      console.error("Upload failed:", error);
      throw error;
    }
  };

  const handleImagePicker = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission Required", "We need access to your gallery.");
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsEditing: false,
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      const asset = result.assets[0];
      await uploadFile(
        asset.uri,
        asset.fileName || `image-${Date.now()}.jpg`,
        asset.mimeType || "image/jpeg"
      );
    }
  };

  const handleCamera = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission Required", "We need camera access.");
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ["images"],
      allowsEditing: false,
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      const asset = result.assets[0];
      await uploadFile(
        asset.uri,
        asset.fileName || `image-${Date.now()}.jpg`,
        asset.mimeType || "image/jpeg"
      );
    }
  };

  const handleDocumentPicker = async () => {
    // TODO: Implement document picker when expo-document-picker is installed
    Alert.alert(
      "Coming Soon",
      "Document upload feature will be available soon."
    );
  };

  const uploadFile = async (
    uri: string,
    fileName: string,
    mimeType: string
  ) => {
    try {
      setIsUploading(true);
      setUploadProgress(0);

      const s3Url = await uploadFileToS3(uri, fileName, mimeType);

      // Update the form field with the S3 URL
      field.onChange(s3Url);

      setIsUploading(false);
      setUploadProgress(100);
    } catch (error) {
      console.error("Failed to upload file:", error);
      setIsUploading(false);
      setUploadProgress(0);
      Alert.alert("Upload Failed", "Failed to upload file. Please try again.");
    }
  };

  const showUploadOptions = () => {
    const options = [];

    if (evidenceType === EvidenceType.Image) {
      options.push(
        { text: "Take Photo", onPress: handleCamera },
        { text: "Choose from Gallery", onPress: handleImagePicker }
      );
    } else if (evidenceType === EvidenceType.Document) {
      options.push({ text: "Choose Document", onPress: handleDocumentPicker });
    } else {
      // For video or mixed types
      options.push(
        { text: "Take Photo", onPress: handleCamera },
        { text: "Choose from Gallery", onPress: handleImagePicker },
        { text: "Choose Document", onPress: handleDocumentPicker }
      );
    }

    options.push({ text: "Cancel", style: "cancel" });

    Alert.alert("Upload Evidence", "Choose upload method:", options);
  };

  const getIconName = () => {
    switch (evidenceType) {
      case EvidenceType.Image:
        return "image-outline";
      case EvidenceType.Video:
        return "videocam-outline";
      case EvidenceType.Document:
        return "document-outline";
      default:
        return "attach-outline";
    }
  };

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}

      <TouchableOpacity
        style={[styles.uploadButton, field.value && styles.uploadButtonSuccess]}
        onPress={showUploadOptions}
        disabled={isUploading}
      >
        {isUploading ? (
          <View style={styles.uploadingContainer}>
            <ActivityIndicator size="small" color={Colors.primary} />
            <Text style={styles.uploadingText}>
              Uploading... {uploadProgress}%
            </Text>
          </View>
        ) : (
          <View style={styles.uploadContent}>
            <Ionicons
              name={field.value ? "checkmark-circle" : getIconName()}
              size={24}
              color={field.value ? Colors.success : Colors.primary}
            />
            <Text
              style={[
                styles.uploadText,
                field.value && styles.uploadTextSuccess,
              ]}
            >
              {field.value ? "Evidence uploaded" : placeholder}
            </Text>
          </View>
        )}
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error.message}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
    marginBottom: 8,
  },
  uploadButton: {
    borderWidth: 2,
    borderColor: Colors.border,
    borderStyle: "dashed",
    borderRadius: 12,
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.background,
  },
  uploadButtonSuccess: {
    borderColor: Colors.success,
    backgroundColor: Colors.successLight,
  },
  uploadContent: {
    alignItems: "center",
    gap: 8,
  },
  uploadText: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: "center",
  },
  uploadTextSuccess: {
    color: Colors.success,
    fontWeight: "500",
  },
  uploadingContainer: {
    alignItems: "center",
    gap: 8,
  },
  uploadingText: {
    fontSize: 14,
    color: Colors.primary,
  },
  errorText: {
    fontSize: 12,
    color: Colors.error,
    marginTop: 4,
  },
});
